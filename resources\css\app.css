@import 'bootstrap/dist/css/bootstrap.min.css';

/* Custom CSS untuk tema sky-blue */
:root {
    --bs-primary: #0ea5e9;
    --bs-primary-rgb: 14, 165, 233;
    --bs-secondary: #64748b;
    --bs-success: #10b981;
    --bs-info: #06b6d4;
    --bs-warning: #f59e0b;
    --bs-danger: #ef4444;
    --bs-light: #f8fafc;
    --bs-dark: #1e293b;
}

.bg-primary {
    background-color: var(--bs-primary) !important;
}

.btn-primary {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

.btn-primary:hover {
    background-color: #0284c7;
    border-color: #0284c7;
}

.text-primary {
    color: var(--bs-primary) !important;
}

.border-primary {
    border-color: var(--bs-primary) !important;
}

/* Sidebar styling */
.sidebar {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    min-height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    transition: transform 0.3s ease-in-out;
}

/* Desktop sidebar */
@media (min-width: 992px) {
    .sidebar {
        position: relative;
        transform: translateX(0) !important;
    }
}

/* Mobile sidebar */
@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(-100%);
        width: 280px !important;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .flex-fill {
        width: 100%;
    }
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    margin: 0.25rem 0;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.sidebar .nav-link i {
    width: 20px;
    margin-right: 10px;
}

/* Sidebar overlay for mobile */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Card styling */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Login page styling */
.login-container {
    background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
    min-height: 100vh;
}

.login-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Stats cards */
.stats-card {
    border-left: 4px solid var(--bs-primary);
    transition: transform 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
}

/* Table styling */
.table th {
    background-color: #f8fafc;
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
    color: #374151;
}

/* Badge styling */
.badge-success {
    background-color: #10b981;
}

.badge-danger {
    background-color: #ef4444;
}

/* Mobile responsive improvements */
@media (max-width: 767.98px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .card-header h5 {
        font-size: 1rem;
    }

    .stats-card .fs-4 {
        font-size: 1rem !important;
    }

    .stats-card h3 {
        font-size: 1.5rem;
    }
}

/* Hamburger button styling */
#sidebarToggle {
    border: none !important;
    box-shadow: none !important;
}

#sidebarToggle:focus {
    box-shadow: none !important;
}