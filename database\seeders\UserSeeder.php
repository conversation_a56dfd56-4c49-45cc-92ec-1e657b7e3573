<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = [
            [
                'employee_id' => 1,
                'username' => 'admin_user',
                'password' => Hash::make('password123'),
                'role' => 'admin',
                'is_active' => true,
            ],
            [
                'employee_id' => 2,
                'username' => 'hr_user',
                'password' => Hash::make('password123'),
                'role' => 'hr',
                'is_active' => true,
            ],
            [
                'employee_id' => 3,
                'username' => 'manager_user',
                'password' => Hash::make('password123'),
                'role' => 'manager',
                'is_active' => true,
            ],
            [
                'employee_id' => 4,
                'username' => 'employee_user',
                'password' => Hash::make('password123'),
                'role' => 'employee',
                'is_active' => true,
            ],
            [
                'employee_id' => null,
                'username' => 'system_user',
                'password' => Hash::make('password123'),
                'role' => 'system',
                'is_active' => true,
            ],
        ];

        foreach ($users as $user) {
            User::create($user);
        }
    }
}
