@extends('layouts.dashboard')

@section('title', 'Data Karyawan')
@section('page-title', 'Data Karyawan')
@section('page-description', 'Kelola data karyawan perusahaan')

@section('content')
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-users me-2"></i>
            <PERSON><PERSON><PERSON>
        </h5>
        <a href="{{ route('employees.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            <PERSON><PERSON>ryawan
        </a>
    </div>

    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Kode</th>
                        <th>Nama</th>
                        <th>Email</th>
                        <th>Departemen</th>
                        <th>Posisi</th>
                        <th>Status</th>
                        <th width="120">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($employees as $employee)
                    <tr>
                        <td class="fw-medium">{{ $employee->employee_code }}</td>
                        <td>{{ $employee->name }}</td>
                        <td class="text-muted">{{ $employee->email ?? '-' }}</td>
                        <td class="text-muted">{{ $employee->department ?? '-' }}</td>
                        <td class="text-muted">{{ $employee->position ?? '-' }}</td>
                        <td>
                            @if($employee->status === 'active')
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>
                                    Aktif
                                </span>
                            @else
                                <span class="badge bg-danger">
                                    <i class="fas fa-times me-1"></i>
                                    Tidak Aktif
                                </span>
                            @endif
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ route('employees.show', $employee) }}"
                                   class="btn btn-outline-info"
                                   title="Lihat Detail">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('employees.edit', $employee) }}"
                                   class="btn btn-outline-warning"
                                   title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('employees.destroy', $employee) }}"
                                      method="POST"
                                      class="d-inline"
                                      onsubmit="return confirm('Apakah Anda yakin ingin menghapus data karyawan ini?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit"
                                            class="btn btn-outline-danger"
                                            title="Hapus">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="text-center py-4 text-muted">
                            <i class="fas fa-inbox fs-1 mb-3 d-block"></i>
                            Tidak ada data karyawan.
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    @if($employees->hasPages())
    <div class="card-footer">
        {{ $employees->links() }}
    </div>
    @endif
</div>
@endsection
