<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

echo "Testing authentication...\n";

// Test 1: Check if user exists
$user = User::where('username', 'admin_user')->first();
if ($user) {
    echo "✓ User found: " . $user->username . "\n";
    echo "✓ User is active: " . ($user->is_active ? 'YES' : 'NO') . "\n";
    echo "✓ Password hash exists: " . (!empty($user->password) ? 'YES' : 'NO') . "\n";
} else {
    echo "✗ User not found\n";
    exit(1);
}

// Test 2: Test authentication methods
echo "\nTesting authentication methods:\n";
echo "Auth identifier name: " . $user->getAuthIdentifierName() . "\n";
echo "Auth identifier: " . $user->getAuthIdentifier() . "\n";
echo "Remember token name: " . $user->getRememberTokenName() . "\n";

// Test 3: Test manual authentication
echo "\nTesting manual authentication...\n";
$credentials = ['username' => 'admin_user', 'password' => 'admin123'];

if (Auth::attempt($credentials)) {
    echo "✓ Authentication successful\n";
    echo "✓ Authenticated user: " . Auth::user()->username . "\n";
} else {
    echo "✗ Authentication failed\n";
    
    // Check if password verification works
    if (Hash::check('admin123', $user->password)) {
        echo "✓ Password verification works\n";
    } else {
        echo "✗ Password verification failed\n";
    }
}

echo "\nTest completed.\n";
