<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Dashboard') - {{ config('app.name', 'Laravel') }}</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-light">
    <!-- Mobile Overlay -->
    <div class="sidebar-overlay d-lg-none" id="sidebarOverlay"></div>

    <div class="d-flex">
        <!-- Sidebar -->
        <div class="sidebar d-lg-block" id="sidebar" style="width: 250px;">
            <div class="p-4">
                <div class="d-flex align-items-center justify-content-between text-white">
                    <div class="d-flex align-items-center">
                        <div class="bg-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                            <i class="fas fa-building text-primary"></i>
                        </div>
                        <h4 class="mb-0 fw-bold">Backoffice</h4>
                    </div>
                    <!-- Close button for mobile -->
                    <button class="btn btn-link text-white d-lg-none p-0" id="sidebarClose">
                        <i class="fas fa-times fs-5"></i>
                    </button>
                </div>
            </div>

            <nav class="px-3">
                <div class="px-3 py-2">
                    <small class="text-white-50 text-uppercase fw-semibold">Menu Utama</small>
                </div>

                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a href="{{ route('dashboard') }}" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                            <i class="fas fa-tachometer-alt"></i>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{{ route('employees.index') }}" class="nav-link {{ request()->routeIs('employees.*') ? 'active' : '' }}">
                            <i class="fas fa-users"></i>
                            Data Karyawan
                        </a>
                    </li>
                </ul>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-fill">
            <!-- Header -->
            <header class="bg-white shadow-sm border-bottom">
                <div class="d-flex justify-content-between align-items-center px-4 py-3">
                    <div class="d-flex align-items-center">
                        <!-- Hamburger Menu for Mobile -->
                        <button class="btn btn-link text-dark d-lg-none me-3 p-0" id="sidebarToggle">
                            <i class="fas fa-bars fs-4"></i>
                        </button>
                        <div>
                            <h2 class="h4 mb-1 text-dark">@yield('page-title', 'Dashboard')</h2>
                            <p class="text-muted small mb-0 d-none d-md-block">@yield('page-description', 'Selamat datang di sistem backoffice')</p>
                        </div>
                    </div>

                    <div class="d-flex align-items-center">
                        <!-- User Info -->
                        <div class="d-flex align-items-center me-3">
                            <div class="text-end me-3 d-none d-md-block">
                                <div class="fw-medium text-dark small">{{ auth()->user()->employee->name ?? auth()->user()->username }}</div>
                                <div class="text-muted small text-capitalize">{{ auth()->user()->role }}</div>
                            </div>
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white fw-bold" style="width: 40px; height: 40px;">
                                {{ strtoupper(substr(auth()->user()->employee->name ?? auth()->user()->username, 0, 1)) }}
                            </div>
                        </div>

                        <!-- Logout -->
                        <form method="POST" action="{{ route('logout') }}" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-sign-out-alt"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="p-4">
                @if (session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if (session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @yield('content')
            </main>
        </div>
    </div>

    <!-- JavaScript for Sidebar Toggle -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebarClose = document.getElementById('sidebarClose');
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            // Toggle sidebar
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            });

            // Close sidebar
            function closeSidebar() {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }

            sidebarClose.addEventListener('click', closeSidebar);
            overlay.addEventListener('click', closeSidebar);

            // Close sidebar when clicking on nav links (mobile)
            const navLinks = sidebar.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth < 992) {
                        closeSidebar();
                    }
                });
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 992) {
                    closeSidebar();
                }
            });
        });
    </script>
</body>
</html>
