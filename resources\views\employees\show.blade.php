@extends('layouts.dashboard')

@section('title', '<PERSON>ail <PERSON>ryawan')
@section('page-title', 'Detail Karyawan')
@section('page-description', 'Informasi lengkap karyawan')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>
                    Informasi Karyawan
                </h5>
                <div class="btn-group">
                    <a href="{{ route('employees.edit', $employee) }}" class="btn btn-warning btn-sm">
                        <i class="fas fa-edit me-2"></i>
                        Edit
                    </a>
                    <a href="{{ route('employees.index') }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left me-2"></i>
                        <PERSON><PERSON><PERSON>
                    </a>
                </div>
            </div>

            <div class="card-body">
                <div class="row">
                    <!-- <PERSON><PERSON> -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-medium">Kode Karyawan</label>
                        <div class="form-control-plaintext bg-light p-2 rounded">
                            {{ $employee->employee_code }}
                        </div>
                    </div>

                    <!-- Nama -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-medium">Nama Lengkap</label>
                        <div class="form-control-plaintext bg-light p-2 rounded">
                            {{ $employee->name }}
                        </div>
                    </div>

                    <!-- Email -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-medium">Email</label>
                        <div class="form-control-plaintext bg-light p-2 rounded">
                            {{ $employee->email ?? '-' }}
                        </div>
                    </div>

                    <!-- Telepon -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-medium">Nomor Telepon</label>
                        <div class="form-control-plaintext bg-light p-2 rounded">
                            {{ $employee->phone ?? '-' }}
                        </div>
                    </div>

                    <!-- Departemen -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-medium">Departemen</label>
                        <div class="form-control-plaintext bg-light p-2 rounded">
                            {{ $employee->department ?? '-' }}
                        </div>
                    </div>

                    <!-- Posisi -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-medium">Posisi/Jabatan</label>
                        <div class="form-control-plaintext bg-light p-2 rounded">
                            {{ $employee->position ?? '-' }}
                        </div>
                    </div>

                    <!-- Tanggal Bergabung -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-medium">Tanggal Bergabung</label>
                        <div class="form-control-plaintext bg-light p-2 rounded">
                            {{ $employee->hire_date ? $employee->hire_date->format('d F Y') : '-' }}
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-medium">Status</label>
                        <div class="form-control-plaintext bg-light p-2 rounded">
                            @if($employee->status === 'active')
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>
                                    Aktif
                                </span>
                            @else
                                <span class="badge bg-danger">
                                    <i class="fas fa-times me-1"></i>
                                    Tidak Aktif
                                </span>
                            @endif
                        </div>
                    </div>

                    <!-- Tanggal Dibuat -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-medium">Tanggal Dibuat</label>
                        <div class="form-control-plaintext bg-light p-2 rounded">
                            {{ $employee->created_at->format('d F Y H:i') }}
                        </div>
                    </div>

                    <!-- Terakhir Diupdate -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-medium">Terakhir Diupdate</label>
                        <div class="form-control-plaintext bg-light p-2 rounded">
                            {{ $employee->updated_at->format('d F Y H:i') }}
                        </div>
                    </div>
                </div>

                @if($employee->user)
                <hr class="my-4">
                <h6 class="mb-3">
                    <i class="fas fa-user-cog me-2"></i>
                    Informasi User
                </h6>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-medium">Username</label>
                        <div class="form-control-plaintext bg-light p-2 rounded">
                            {{ $employee->user->username }}
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-medium">Role</label>
                        <div class="form-control-plaintext bg-light p-2 rounded">
                            <span class="badge bg-primary text-capitalize">{{ $employee->user->role }}</span>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
