@extends('layouts.dashboard')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')
@section('page-description', 'Ringkasan data sistem backoffice')

@section('content')
<div class="row mb-4">
    <!-- Total Karyawan -->
    <div class="col-md-4 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                        <i class="fas fa-users text-primary fs-4"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">Total Karyawan</h6>
                        <h3 class="mb-0 fw-bold">{{ $totalEmployees }}</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Karyawan Aktif -->
    <div class="col-md-4 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-success bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                        <i class="fas fa-user-check text-success fs-4"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">Karyawan Aktif</h6>
                        <h3 class="mb-0 fw-bold">{{ $activeEmployees }}</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Users -->
    <div class="col-md-4 mb-3">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-info bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                        <i class="fas fa-user-cog text-info fs-4"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-1">Total Users</h6>
                        <h3 class="mb-0 fw-bold">{{ $totalUsers }}</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-bolt me-2"></i>
            Aksi Cepat
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6 col-lg-3 mb-3">
                <a href="{{ route('employees.create') }}" class="text-decoration-none">
                    <div class="card border-primary border-2 h-100">
                        <div class="card-body text-center">
                            <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 50px; height: 50px;">
                                <i class="fas fa-plus text-white"></i>
                            </div>
                            <h6 class="card-title text-primary">Tambah Karyawan</h6>
                            <p class="card-text text-muted small">Tambah data karyawan baru</p>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-md-6 col-lg-3 mb-3">
                <a href="{{ route('employees.index') }}" class="text-decoration-none">
                    <div class="card border-success border-2 h-100">
                        <div class="card-body text-center">
                            <div class="bg-success rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 50px; height: 50px;">
                                <i class="fas fa-list text-white"></i>
                            </div>
                            <h6 class="card-title text-success">Lihat Karyawan</h6>
                            <p class="card-text text-muted small">Daftar semua karyawan</p>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Welcome Message -->
<div class="card bg-primary text-white">
    <div class="card-body">
        <div class="d-flex align-items-center">
            <div class="me-4">
                <i class="fas fa-hand-wave fs-1"></i>
            </div>
            <div>
                <h4 class="card-title mb-2">Selamat Datang, {{ auth()->user()->employee->name ?? auth()->user()->username }}!</h4>
                <p class="card-text mb-0">
                    Anda login sebagai <span class="fw-bold text-capitalize">{{ auth()->user()->role }}</span>.
                    Gunakan menu di sebelah kiri untuk mengakses fitur-fitur sistem.
                </p>
            </div>
        </div>
    </div>
</div>
@endsection
