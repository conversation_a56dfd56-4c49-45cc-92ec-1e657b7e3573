# Informasi Login Sistem Backoffice

## Data Login yang Tersedia

Berikut adalah data login yang sudah tersedia di sistem:

### 1. Admin User
- **Username**: `admin_user`
- **Password**: `password123`
- **Role**: Admin
- **Employee**: <PERSON> (EMP001)

### 2. HR User
- **Username**: `hr_user`
- **Password**: `password123`
- **Role**: HR
- **Employee**: <PERSON> (EMP002)

### 3. Manager User
- **Username**: `manager_user`
- **Password**: `password123`
- **Role**: Manager
- **Employee**: <PERSON> (EMP003)

### 4. Employee User
- **Username**: `employee_user`
- **Password**: `password123`
- **Role**: Employee
- **Employee**: <PERSON> (EMP004)

### 5. System User
- **Username**: `system_user`
- **Password**: `password123`
- **Role**: System
- **Employee**: -

## Cara Mengakses

1. Buka browser dan akses: `http://127.0.0.1:8000`
2. Masukkan username dan password dari daftar di atas
3. Klik tombol "Masuk"
4. Anda akan diarahkan ke dashboard

## Fitur yang Tersedia

### Dashboard
- Ringkasan data karyawan
- Statistik sistem
- Quick actions

### Data Karyawan
- Lihat daftar karyawan
- Tambah karyawan baru
- Edit data karyawan
- Hapus karyawan
- Detail karyawan

## Teknologi yang Digunakan

- **Backend**: Laravel 11
- **Frontend**: Bootstrap 5.3 dengan Font Awesome icons
- **Database**: MySQL
- **Authentication**: Laravel UI dengan username-based login
- **Styling**: Sky-blue dan white theme sesuai permintaan

## 📱 Responsive Design

### Desktop (≥992px)
- Sidebar tetap terlihat di sebelah kiri
- Layout full-width dengan navigation yang mudah diakses
- User info lengkap ditampilkan di header

### Mobile & Tablet (<992px)
- **Hamburger Menu** - Icon ☰ di header untuk membuka sidebar
- **Overlay Background** - Background gelap saat sidebar terbuka
- **Auto Close** - Sidebar otomatis tertutup saat:
  - Mengklik link navigation
  - Mengklik overlay background
  - Mengklik tombol close (×)
- **Touch-friendly** - Button dan link yang mudah diakses
- **Responsive Tables** - Tabel dapat di-scroll horizontal
- **Compact Layout** - User info dan description disembunyikan untuk menghemat ruang

### Fitur Hamburger Menu
1. **Buka Sidebar** - Klik icon hamburger (☰) di header
2. **Tutup Sidebar** - Klik tombol close (×), overlay, atau link navigation
3. **Smooth Animation** - Transisi yang halus saat membuka/menutup
4. **Auto Resize** - Sidebar otomatis menyesuaikan saat ukuran layar berubah

## 📦 Assets & Performance

- ✅ Bootstrap CSS dan JS sudah ter-compile
- ✅ Font Awesome icons dari CDN
- ✅ Custom CSS untuk tema sky-blue dan responsive design
- ✅ Vite hot reload untuk development
- ✅ Optimized untuk mobile dan desktop
