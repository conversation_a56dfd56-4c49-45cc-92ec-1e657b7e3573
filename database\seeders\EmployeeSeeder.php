<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Employee;

class EmployeeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $employees = [
            [
                'employee_code' => 'EMP001',
                'name' => 'John Admin',
                'email' => '<EMAIL>',
                'phone' => '081234567890',
                'department' => 'IT',
                'position' => 'Administrator',
                'hire_date' => '2020-01-15',
                'status' => 'active',
            ],
            [
                'employee_code' => 'EMP002',
                'name' => 'Sarah HR',
                'email' => '<EMAIL>',
                'phone' => '081234567891',
                'department' => 'HR',
                'position' => 'HR Officer',
                'hire_date' => '2021-05-10',
                'status' => 'active',
            ],
            [
                'employee_code' => 'EMP003',
                'name' => 'Michael <PERSON>',
                'email' => '<EMAIL>',
                'phone' => '081234567892',
                'department' => 'Sales',
                'position' => 'Sales Manager',
                'hire_date' => '2019-03-20',
                'status' => 'active',
            ],
            [
                'employee_code' => 'EMP004',
                'name' => 'Lisa Employee',
                'email' => '<EMAIL>',
                'phone' => '081234567893',
                'department' => 'Support',
                'position' => 'Customer Support',
                'hire_date' => '2022-02-01',
                'status' => 'active',
            ],
        ];

        foreach ($employees as $employee) {
            Employee::create($employee);
        }
    }
}
